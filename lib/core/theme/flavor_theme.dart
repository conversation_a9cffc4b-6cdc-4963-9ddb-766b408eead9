import 'package:flutter/material.dart';
import '../../flavors.dart';
import 'color_pallette.dart';

/// Flavor-specific theme configurations
/// Provides different color schemes, spacing, and styling based on app flavor
class FlavorTheme {
  
  /// Get flavor-specific primary color
  static Color getPrimaryColor() {
    switch (F.appFlavor) {
      case Flavor.sis:
        return const Color(0xFF2E7D32); // SIS Green
      case Flavor.cfroex:
        return const Color(0xFF1976D2); // Forex Blue
      case Flavor.ncm:
        return const Color(0xFF7B1FA2); // NCM Purple
      case Flavor.sf_app:
        return ColorPalette.primaryColor; // Default
    }
  }

  /// Get flavor-specific accent color
  static Color getAccentColor() {
    switch (F.appFlavor) {
      case Flavor.sis:
        return const Color(0xFF4CAF50); // Light SIS Green
      case Flavor.cfroex:
        return const Color(0xFF2196F3); // Light Forex Blue
      case Flavor.ncm:
        return const Color(0xFF9C27B0); // Light NCM Purple
      case Flavor.sf_app:
        return ColorPalette.primaryColor; // Default
    }
  }

  /// Get flavor-specific gradient colors
  static List<Color> getGradientColors() {
    switch (F.appFlavor) {
      case Flavor.sis:
        return [
          const Color(0xFF1B5E20),
          const Color(0xFF2E7D32),
          const Color(0xFF4CAF50),
        ];
      case Flavor.cfroex:
        return [
          const Color(0xFF0D47A1),
          const Color(0xFF1976D2),
          const Color(0xFF2196F3),
        ];
      case Flavor.ncm:
        return [
          const Color(0xFF4A148C),
          const Color(0xFF7B1FA2),
          const Color(0xFF9C27B0),
        ];
      case Flavor.sf_app:
        return [
          ColorPalette.primaryColor,
          ColorPalette.primaryColor.withOpacity(0.8),
        ];
    }
  }

  /// Get flavor-specific button styling
  static ButtonStyle getButtonStyle() {
    return ElevatedButton.styleFrom(
      backgroundColor: getPrimaryColor(),
      foregroundColor: Colors.white,
      elevation: F.appFlavor == Flavor.sis ? 2 : 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(
          F.appFlavor == Flavor.sis ? 12 : 8,
        ),
      ),
    );
  }

  /// Get flavor-specific card styling
  static BoxDecoration getCardDecoration() {
    return BoxDecoration(
      borderRadius: BorderRadius.circular(
        F.appFlavor == Flavor.sis ? 16 : 12,
      ),
      gradient: F.appFlavor == Flavor.sis
          ? LinearGradient(
              colors: getGradientColors(),
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            )
          : null,
      color: F.appFlavor != Flavor.sis ? Colors.white : null,
      boxShadow: [
        BoxShadow(
          color: getPrimaryColor().withOpacity(0.1),
          blurRadius: F.appFlavor == Flavor.sis ? 8 : 4,
          offset: const Offset(0, 2),
        ),
      ],
    );
  }

  /// Get flavor-specific text styles
  static TextStyle getHeadingStyle() {
    return TextStyle(
      fontSize: F.appFlavor == Flavor.sis ? 24 : 20,
      fontWeight: F.appFlavor == Flavor.sis ? FontWeight.w600 : FontWeight.w500,
      color: getPrimaryColor(),
    );
  }

  /// Get flavor-specific spacing
  static double getSpacing() {
    switch (F.appFlavor) {
      case Flavor.sis:
        return 16.0;
      case Flavor.cfroex:
        return 12.0;
      case Flavor.ncm:
        return 14.0;
      case Flavor.sf_app:
        return 12.0;
    }
  }

  /// Get flavor-specific border radius
  static double getBorderRadius() {
    switch (F.appFlavor) {
      case Flavor.sis:
        return 16.0;
      case Flavor.cfroex:
        return 8.0;
      case Flavor.ncm:
        return 12.0;
      case Flavor.sf_app:
        return 8.0;
    }
  }
}
